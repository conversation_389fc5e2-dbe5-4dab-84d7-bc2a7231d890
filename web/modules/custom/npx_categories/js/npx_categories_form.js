(function ($, Drupal, once) {
  Drupal.behaviors.npxCategoriesForm = {
    attach: function (context, settings) {
      // Handle form submission
      once('npx-categories-form', '#missing-categories-form', context).forEach(
        function (form) {
          var $form = $(form);
          $form.on('submit', function (e) {
            e.preventDefault();
            $.ajax({
              url: $form.attr('action'),
              type: 'POST',
              data: $form.serialize(),
              success: function (response) {
                // Replace modal content with a success message.
                var $modal = $form.closest('.npx-categories-form-wrapper');
                $modal.html('<p>Formularz został wysłany.</p>');
              },
              error: function (xhr, status, error) {
                // Handle form submission error.
                var $modal = $form.closest('.npx-categories-form-wrapper');
                $modal.html(
                  '<p>Błąd przy wysyłaniu formularza kategorii. Spróbuj później.</p>'
                );
              },
            });
          });
        }
      );

      // Handle link click to open form in a popup
      once('npx-categories-form-link', '.npx-categories-link', context).forEach(
        function (link) {
          var $link = $(link);
          $link.on('click', function (e) {
            e.preventDefault();
            // Open the form in a popup.
            $.ajax({
              url: '/npx-categories/form',
              type: 'GET',
              success: function (data) {
                // Display the form in a modal or popup.
                var $modal = $('<div>', { class: 'npx-categories-modal' }).html(
                  data
                );
                $('body').append($modal);
                $modal.dialog({
                  modal: true,
                  title: 'Brakujące kategorie?',
                  width: 380,
                  close: function () {
                    $modal.remove();
                  },
                });
              },
              error: function (xhr, status, error) {
                // Handle error
                alert('Failed to load the form. Please try again.');
              },
            });
          });
        }
      );
    }
  };
})(jQuery, Drupal, once);