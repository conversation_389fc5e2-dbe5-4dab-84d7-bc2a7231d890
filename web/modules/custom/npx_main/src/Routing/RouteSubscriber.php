<?php

namespace Drupal\npx_main\Routing;

use <PERSON><PERSON>al\Core\Routing\RouteSubscriberBase;
use Symfony\Component\Routing\RouteCollection;
use Symfony\Component\Routing\Route;
use Drupal\Core\Routing\RoutingEvents;

/**
 * Class RouteSubscriber.
 */
class RouteSubscriber extends RouteSubscriberBase {

  
  public static function getSubscribedEvents() : array {
    
    $events[RoutingEvents::ALTER] = [
      'onAlterRoutes',
      110,
    ];
    return $events;
  }
  
  /**
   * {@inheritdoc}
   */
  protected function alterRoutes(RouteCollection $collection) {
    // Replace opigno user profile route.
    $opigno_user_page_key = 'opigno_statistics.user';
    $opigno_user_page = $collection->get($opigno_user_page_key);
    if(isset($opigno_user_page)) {
      $collection->remove($opigno_user_page_key);
    }
  }
}
