.inquiry-access-title {
  @include h3New;
}

.inquiry-page .body {
  @include vw-background(#F4F9FF);
  padding: map-get($spacers, 3) 0;
  
  p, li {
    @include t2;
    color: $color-primary;
  }
}

body.adminimal-admin-toolbar .inquiry-page .body {
  @include vw-background(#F4F9FF);
  padding: map-get($spacers, 3) 0;
  
  p, li {
    @include t2;
    color: $color-primary;
  }
}

.inquiry-page .field-section-04 {
  padding: map-get($spacers, 5) 0;
}

.inquiry-page .field-section-04.row,
.field-section-04.row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: map-get($spacers, 4);
  margin: 0;
  padding: map-get($spacers, 5) 0;
  
  .col-md-4 {
    flex: 0 0 540px;
    max-width: 540px;
    margin-bottom: map-get($spacers, 4);
    
    @include media-breakpoint-down(md) {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
  
  .paragraph--type--tekst-z-ikona-frontpage {
    @include list-marks-inquiry;
    width: 100%;
    border-radius: 16px;
    box-sizing: border-box;
    box-shadow: 0 8px 24px rgba(125, 125, 125, 0.15);
    display: flex;
    flex-direction: column;
    justify-items: center;
    text-align: center;
    margin: 0;
    padding: map-get($spacers, 4) map-get($spacers, 3);
    padding-bottom: 80px;
    position: relative;
    background-color: $white;
    
    ul {
      padding: map-get($spacers, 2);
      list-style: none;
    }
    
    li {
      @include t2;
      color: $color-primary;
      position: relative;
      padding-left: 1.5rem;
      list-style: none;
      text-align: left;
      margin-bottom: map-get($spacers, 2);
      
      &:before {
        content: '';
        display: block;
        position: absolute;
        width: 18px;
        height: 18px;
        top: 2px;
        left: 0;
        background-image: url('../src/images/checkmark-circle.svg');
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        transform: none;
        border: none;
      }
      
      &:after { display: none; }
    }
    
    h4 {
      @include h4New;
      display: block;
      margin: 0;
      width: 100%;
      height: auto;
      &:after { content: none; }
    }
    
    p {
      font-weight: $font-weight-normal;
      font-size: 1rem;
      line-height: 1.5;
      color: $color-primary;
    }
    

  }
}

// Button style for registration button in inquiry cards
.btn-inquiry-registration {
  display: block;
  width: 100%;
  position: absolute;
  bottom: map-get($spacers, 3);
  left: 0;
  right: 0;
  margin: 0;
  padding: map-get($spacers, 3);
  background-color: $color-secondary;
  color: $white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: background-color 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: $color-primary;
    color: $white;
    text-decoration: none;
  }
}

.inquiry-page .field-text-indexed {
  @include list-marks-inquiry;
  
  ul {
    padding: map-get($spacers, 2);
    list-style: none;
  }
  
  li {
    @include t2;
    color: $color-primary;
    position: relative;
    padding-left: 1.5rem;
    list-style: none;
    text-align: left;
    margin-bottom: map-get($spacers, 2);
    
    &:before {
      content: '';
      display: block;
      position: absolute;
      width: 18px;
      height: 18px;
      top: 2px;
      left: 0;
      background-image: url('../src/images/checkmark-circle.svg');
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      transform: none;
      border: none;
    }
    
    &:after { display: none; }
  }
} 